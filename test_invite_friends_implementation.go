package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo/activity_cashback"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo/agent_referral"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service/affiliate"
	"gorm.io/gorm"
)

func main() {
	fmt.Println("Testing Invite Friends Implementation...")

	// Initialize database connection (you'll need to set this up)
	// global.GVA_DB = ... your database connection

	ctx := context.Background()

	// Test the complete flow
	if err := testInviteFriendsFlow(ctx); err != nil {
		log.Fatalf("Test failed: %v", err)
	}

	fmt.Println("✅ All tests passed!")
}

func testInviteFriendsFlow(ctx context.Context) error {
	fmt.Println("\n🧪 Testing Invite Friends Flow...")

	// Step 1: Create test users
	referrerID := uuid.New()
	invitedUserID := uuid.New()

	fmt.Printf("👤 Referrer ID: %s\n", referrerID.String())
	fmt.Printf("👤 Invited User ID: %s\n", invitedUserID.String())

	// Step 2: Create referral relationship
	if err := createTestReferralRelationship(ctx, referrerID, invitedUserID); err != nil {
		return fmt.Errorf("failed to create referral relationship: %w", err)
	}
	fmt.Println("✅ Referral relationship created")

	// Step 3: Verify invite friends task exists
	taskRepo := activity_cashback.NewActivityTaskRepository()
	task, err := taskRepo.GetByTaskIdentifier(ctx, model.TaskIDInviteFriends)
	if err != nil {
		return fmt.Errorf("failed to get invite friends task: %w", err)
	}
	fmt.Printf("✅ Invite Friends task found: %s (Points: %d)\n", task.Name, task.Points)

	// Step 4: Test the affiliate service logic
	affiliateService := affiliate.NewActivityCashbackService()
	
	// Create a mock affiliate transaction to trigger FirstTransactionAt update
	mockTx := &model.AffiliateTransaction{
		UserID: invitedUserID,
		Status: "Completed",
	}

	// This should trigger the invite friends task completion
	if err := affiliateService.ProcessActivityTransactionCashback(ctx, mockTx); err != nil {
		return fmt.Errorf("failed to process affiliate transaction: %w", err)
	}
	fmt.Println("✅ Affiliate transaction processed")

	// Step 5: Verify task completion was created
	unlimitedRepo := activity_cashback.NewUnlimitedTaskCompletionRepository()
	completions, err := unlimitedRepo.GetByUserAndTask(ctx, referrerID, task.ID, 10, 0)
	if err != nil {
		return fmt.Errorf("failed to get task completions: %w", err)
	}

	if len(completions) == 0 {
		return fmt.Errorf("no task completions found for referrer")
	}

	completion := completions[0]
	fmt.Printf("✅ Task completion created: Points awarded = %d\n", completion.PointsAwarded)

	// Step 6: Verify verification data
	if completion.VerificationData == nil {
		return fmt.Errorf("verification data is nil")
	}

	fmt.Printf("✅ Verification data: Method = %s, Source = %s\n", 
		completion.VerificationData.VerificationMethod,
		completion.VerificationData.VerificationSource)

	return nil
}

func createTestReferralRelationship(ctx context.Context, referrerID, invitedUserID uuid.UUID) error {
	// Create test users if they don't exist
	users := []*model.User{
		{
			ID:             referrerID,
			InvitationCode: stringPtr("REFERRER123"),
		},
		{
			ID: invitedUserID,
		},
	}

	for _, user := range users {
		err := global.GVA_DB.WithContext(ctx).FirstOrCreate(user, "id = ?", user.ID).Error
		if err != nil {
			return fmt.Errorf("failed to create user %s: %w", user.ID.String(), err)
		}
	}

	// Create referral relationship
	referral := &model.Referral{
		UserID:     invitedUserID,
		ReferrerID: &referrerID,
		Depth:      1,
		CreatedAt:  time.Now(),
	}

	err := global.GVA_DB.WithContext(ctx).FirstOrCreate(referral, 
		"user_id = ? AND referrer_id = ?", invitedUserID, referrerID).Error
	if err != nil {
		return fmt.Errorf("failed to create referral: %w", err)
	}

	return nil
}

func stringPtr(s string) *string {
	return &s
}

// Test helper functions
func testGetDirectReferrers(ctx context.Context) error {
	fmt.Println("\n🧪 Testing GetDirectReferrers...")

	invitationRepo := &agent_referral.InvitationRepository{}
	
	// Test with a user that has referrers
	testUserID := uuid.New()
	referrerID := uuid.New()

	// Create test referral
	referral := &model.Referral{
		UserID:     testUserID,
		ReferrerID: &referrerID,
		Depth:      1,
	}

	if err := global.GVA_DB.WithContext(ctx).Create(referral).Error; err != nil {
		return fmt.Errorf("failed to create test referral: %w", err)
	}

	// Test GetDirectReferrers
	referrers, err := invitationRepo.GetDirectReferrers(ctx, testUserID)
	if err != nil {
		return fmt.Errorf("GetDirectReferrers failed: %w", err)
	}

	if len(referrers) != 1 {
		return fmt.Errorf("expected 1 referrer, got %d", len(referrers))
	}

	if referrers[0] != referrerID {
		return fmt.Errorf("expected referrer %s, got %s", referrerID.String(), referrers[0].String())
	}

	fmt.Printf("✅ GetDirectReferrers returned %d referrers\n", len(referrers))
	return nil
}

func testTaskRepository(ctx context.Context) error {
	fmt.Println("\n🧪 Testing Task Repository...")

	taskRepo := activity_cashback.NewActivityTaskRepository()
	
	// Test GetByTaskIdentifier
	task, err := taskRepo.GetByTaskIdentifier(ctx, model.TaskIDInviteFriends)
	if err != nil {
		return fmt.Errorf("GetByTaskIdentifier failed: %w", err)
	}

	if task.TaskIdentifier == nil || *task.TaskIdentifier != model.TaskIDInviteFriends {
		return fmt.Errorf("wrong task identifier returned")
	}

	fmt.Printf("✅ Task found: %s (ID: %s, Points: %d)\n", 
		task.Name, task.ID.String(), task.Points)

	return nil
}
