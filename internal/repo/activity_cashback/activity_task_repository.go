package activity_cashback

import (
	"context"
	"time"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gorm.io/gorm"
)

// ActivityTaskRepository implements ActivityTaskRepositoryInterface
type ActivityTaskRepository struct {
	db *gorm.DB
}

// NewActivityTaskRepository creates a new ActivityTaskRepository
func NewActivityTaskRepository() ActivityTaskRepositoryInterface {
	return &ActivityTaskRepository{
		db: global.GVA_DB,
	}
}

// Create creates a new activity task
func (r *ActivityTaskRepository) Create(ctx context.Context, task *model.ActivityTask) error {
	return r.db.WithContext(ctx).Create(task).Error
}

// Update updates an existing activity task
func (r *ActivityTaskRepository) Update(ctx context.Context, task *model.ActivityTask) error {
	return r.db.WithContext(ctx).Save(task).Error
}

// Delete soft deletes an activity task
func (r *ActivityTaskRepository) Delete(ctx context.Context, id uuid.UUID) error {
	return r.db.WithContext(ctx).Model(&model.ActivityTask{}).
		Where("id = ?", id).
		Update("is_active", false).Error
}

// GetByID retrieves an activity task by ID
func (r *ActivityTaskRepository) GetByID(ctx context.Context, id uuid.UUID) (*model.ActivityTask, error) {
	var task model.ActivityTask
	err := r.db.WithContext(ctx).
		Preload("Category").
		First(&task, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &task, nil
}

// GetByCategoryID retrieves tasks by category ID
func (r *ActivityTaskRepository) GetByCategoryID(ctx context.Context, categoryID uint) ([]model.ActivityTask, error) {
	var tasks []model.ActivityTask
	err := r.db.WithContext(ctx).
		Preload("Category").
		Where("category_id = ? AND is_active = ?", categoryID, true).
		Order("sort_order ASC, created_at ASC").
		Find(&tasks).Error
	return tasks, err
}

// GetActive retrieves all active tasks
func (r *ActivityTaskRepository) GetActive(ctx context.Context) ([]model.ActivityTask, error) {
	var tasks []model.ActivityTask
	err := r.db.WithContext(ctx).
		Preload("Category").
		Where("is_active = ?", true).
		Order("sort_order ASC, created_at ASC").
		Find(&tasks).Error
	return tasks, err
}

// GetAvailable retrieves tasks that are currently available (active, started, not expired)
func (r *ActivityTaskRepository) GetAvailable(ctx context.Context, now time.Time) ([]model.ActivityTask, error) {
	var tasks []model.ActivityTask
	err := r.db.WithContext(ctx).
		Preload("Category").
		Where("is_active = ? AND (start_date IS NULL OR start_date <= ?) AND (end_date IS NULL OR end_date >= ?)",
			true, now, now).
		Order("sort_order ASC, created_at ASC").
		Find(&tasks).Error
	return tasks, err
}

// GetAll retrieves all tasks
func (r *ActivityTaskRepository) GetAll(ctx context.Context) ([]model.ActivityTask, error) {
	var tasks []model.ActivityTask
	err := r.db.WithContext(ctx).
		Preload("Category").
		Order("sort_order ASC, created_at ASC").
		Find(&tasks).Error
	return tasks, err
}

// GetTasksForUser retrieves available tasks for a specific user
func (r *ActivityTaskRepository) GetTasksForUser(ctx context.Context, userID uuid.UUID) ([]model.ActivityTask, error) {
	now := time.Now()
	var allTasks []model.ActivityTask

	// Get available tasks and left join with user progress to include progress info
	err := r.db.WithContext(ctx).
		Preload("Category").
		Preload("UserProgress", "user_id = ?", userID).
		Where("is_active = ? AND (start_date IS NULL OR start_date <= ?) AND (end_date IS NULL OR end_date >= ?)",
			true, now, now).
		Order("sort_order ASC, created_at ASC").
		Find(&allTasks).Error

	if err != nil {
		return nil, err
	}

	// Filter consecutive check-in tasks based on user's current progress
	filteredTasks, err := r.filterConsecutiveCheckInTasks(ctx, userID, allTasks)
	if err != nil {
		return nil, err
	}

	return filteredTasks, nil
}

// filterConsecutiveCheckInTasks filters consecutive check-in tasks based on user's current progress
// Only shows the appropriate consecutive task based on user's current streak
func (r *ActivityTaskRepository) filterConsecutiveCheckInTasks(ctx context.Context, userID uuid.UUID, tasks []model.ActivityTask) ([]model.ActivityTask, error) {
	var filteredTasks []model.ActivityTask
	var consecutiveTasks []model.ActivityTask
	var otherTasks []model.ActivityTask

	// Separate consecutive check-in tasks from other tasks
	for _, task := range tasks {
		if task.TaskIdentifier != nil {
			switch *task.TaskIdentifier {
			case model.TaskIDConsecutiveCheckin3, model.TaskIDConsecutiveCheckin7, model.TaskIDConsecutiveCheckin30:
				consecutiveTasks = append(consecutiveTasks, task)
			default:
				otherTasks = append(otherTasks, task)
			}
		} else {
			otherTasks = append(otherTasks, task)
		}
	}

	// If no consecutive tasks, return all other tasks
	if len(consecutiveTasks) == 0 {
		return otherTasks, nil
	}

	// Get user's current consecutive check-in streak
	currentStreak, err := r.getUserConsecutiveStreak(ctx, userID)
	if err != nil {
		// If error getting streak, default to showing 3-day task
		currentStreak = 0
	}

	// Determine which consecutive task to show based on current streak
	var targetTask *model.ActivityTask
	for _, task := range consecutiveTasks {
		if task.TaskIdentifier != nil {
			switch *task.TaskIdentifier {
			case model.TaskIDConsecutiveCheckin3:
				if currentStreak < 3 || currentStreak >= 30 {
					// Show 3-day task for new users or after completing 30-day cycle
					targetTask = &task
				}
			case model.TaskIDConsecutiveCheckin7:
				if currentStreak >= 3 && currentStreak < 7 {
					targetTask = &task
				}
			case model.TaskIDConsecutiveCheckin30:
				if currentStreak >= 7 && currentStreak < 30 {
					targetTask = &task
				}
			}
		}
	}

	// Add the appropriate consecutive task if found
	if targetTask != nil {
		filteredTasks = append(filteredTasks, *targetTask)
	}

	// Add all other tasks
	filteredTasks = append(filteredTasks, otherTasks...)

	return filteredTasks, nil
}

// getUserConsecutiveStreak gets the user's current consecutive check-in streak
func (r *ActivityTaskRepository) getUserConsecutiveStreak(ctx context.Context, userID uuid.UUID) (int, error) {
	var progress model.UserTaskProgress

	// Get the most recent consecutive check-in progress
	// We check all consecutive tasks and return the highest streak
	err := r.db.WithContext(ctx).
		Joins("JOIN activity_tasks ON user_task_progress.task_id = activity_tasks.id").
		Where("user_task_progress.user_id = ? AND activity_tasks.task_identifier IN (?)",
			userID, []string{
				string(model.TaskIDConsecutiveCheckin3),
				string(model.TaskIDConsecutiveCheckin7),
				string(model.TaskIDConsecutiveCheckin30),
			}).
		Order("user_task_progress.streak_count DESC").
		First(&progress).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return 0, nil // No streak found, return 0
		}
		return 0, err
	}

	return progress.StreakCount, nil
}

// GetDailyTasks retrieves daily tasks (by category name)
func (r *ActivityTaskRepository) GetDailyTasks(ctx context.Context) ([]model.ActivityTask, error) {
	var tasks []model.ActivityTask
	err := r.db.WithContext(ctx).
		Preload("Category").
		Joins("JOIN task_categories ON activity_tasks.category_id = task_categories.id").
		Where("task_categories.name = ? AND activity_tasks.is_active = ?", "daily", true).
		Order("activity_tasks.sort_order ASC, activity_tasks.created_at ASC").
		Find(&tasks).Error
	return tasks, err
}

// GetCommunityTasks retrieves community tasks (by category name)
func (r *ActivityTaskRepository) GetCommunityTasks(ctx context.Context) ([]model.ActivityTask, error) {
	var tasks []model.ActivityTask
	err := r.db.WithContext(ctx).
		Preload("Category").
		Joins("JOIN task_categories ON activity_tasks.category_id = task_categories.id").
		Where("task_categories.name = ? AND activity_tasks.is_active = ?", "community", true).
		Order("activity_tasks.sort_order ASC, activity_tasks.created_at ASC").
		Find(&tasks).Error
	return tasks, err
}

// GetTradingTasks retrieves trading tasks (by category name)
func (r *ActivityTaskRepository) GetTradingTasks(ctx context.Context) ([]model.ActivityTask, error) {
	var tasks []model.ActivityTask
	err := r.db.WithContext(ctx).
		Preload("Category").
		Joins("JOIN task_categories ON activity_tasks.category_id = task_categories.id").
		Where("task_categories.name = ? AND activity_tasks.is_active = ?", "trading", true).
		Order("activity_tasks.sort_order ASC, activity_tasks.created_at ASC").
		Find(&tasks).Error
	return tasks, err
}
