package activity_cashback

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
)

func TestAccumulatedTradingTaskTargetValue(t *testing.T) {
	tests := []struct {
		name           string
		taskConditions *model.TaskConditions
		expectedTarget int
	}{
		{
			name: "ACCUMULATED_TRADING_10K should have target 10",
			taskConditions: &model.TaskConditions{
				MinTradingVolume: func() *float64 { v := 10.0; return &v }(),
			},
			expectedTarget: 10,
		},
		{
			name: "ACCUMULATED_TRADING_50K should have target 50",
			taskConditions: &model.TaskConditions{
				MinTradingVolume: func() *float64 { v := 50.0; return &v }(),
			},
			expectedTarget: 50,
		},
		{
			name: "ACCUMULATED_TRADING_100K should have target 100",
			taskConditions: &model.TaskConditions{
				MinTradingVolume: func() *float64 { v := 100.0; return &v }(),
			},
			expectedTarget: 100,
		},
		{
			name: "ACCUMULATED_TRADING_500K should have target 500",
			taskConditions: &model.TaskConditions{
				MinTradingVolume: func() *float64 { v := 500.0; return &v }(),
			},
			expectedTarget: 500,
		},
		{
			name: "Task with RequiredTradeCount should use that",
			taskConditions: &model.TaskConditions{
				RequiredTradeCount: func() *int { v := 5; return &v }(),
				MinTradingVolume:   func() *float64 { v := 10000.0; return &v }(),
			},
			expectedTarget: 5,
		},
		{
			name: "Task with ConsecutiveDays should use that",
			taskConditions: &model.TaskConditions{
				ConsecutiveDays:  func() *int { v := 7; return &v }(),
				MinTradingVolume: func() *float64 { v := 10000.0; return &v }(),
			},
			expectedTarget: 7,
		},
		{
			name:           "Task with no conditions should default to 1",
			taskConditions: nil,
			expectedTarget: 1,
		},
		{
			name:           "Task with empty conditions should default to 1",
			taskConditions: &model.TaskConditions{},
			expectedTarget: 1,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Simulate the logic from InitializeTaskProgress
			var targetValue *int

			if tt.taskConditions != nil {
				if tt.taskConditions.RequiredTradeCount != nil {
					targetValue = tt.taskConditions.RequiredTradeCount
				} else if tt.taskConditions.ConsecutiveDays != nil {
					targetValue = tt.taskConditions.ConsecutiveDays
				} else if tt.taskConditions.MinTradingVolume != nil {
					// For accumulated trading tasks, target value should be the milestone amount
					targetVolume := int(*tt.taskConditions.MinTradingVolume)
					targetValue = &targetVolume
				}
			}

			// Set default target value if not specified
			if targetValue == nil {
				defaultTarget := 1
				targetValue = &defaultTarget
			}

			assert.Equal(t, tt.expectedTarget, *targetValue, "Target value should match expected")
		})
	}
}

func TestProgressValueLogic(t *testing.T) {
	tests := []struct {
		name              string
		accumulatedVolume float64
		milestone         float64
		expectedProgress  int
		shouldComplete    bool
	}{
		{
			name:              "Volume below milestone should set progress to volume",
			accumulatedVolume: 41.58,
			milestone:         10000,
			expectedProgress:  41,
			shouldComplete:    false,
		},
		{
			name:              "Volume at milestone should set progress to milestone",
			accumulatedVolume: 10000,
			milestone:         10000,
			expectedProgress:  10000,
			shouldComplete:    true,
		},
		{
			name:              "Volume above milestone should set progress to milestone",
			accumulatedVolume: 15000,
			milestone:         10000,
			expectedProgress:  10000,
			shouldComplete:    true,
		},
		{
			name:              "Small volume should not complete high milestone",
			accumulatedVolume: 100,
			milestone:         50000,
			expectedProgress:  100,
			shouldComplete:    false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Simulate the logic from processAccumulatedTrading
			progressValue := int(tt.accumulatedVolume)
			if tt.accumulatedVolume >= tt.milestone {
				progressValue = int(tt.milestone)
			}

			shouldComplete := tt.accumulatedVolume >= tt.milestone

			assert.Equal(t, tt.expectedProgress, progressValue, "Progress value should match expected")
			assert.Equal(t, tt.shouldComplete, shouldComplete, "Completion status should match expected")
		})
	}
}

func TestRealWorldScenario(t *testing.T) {
	// Test case based on the user's data
	accumulatedVolume := 41.58
	targetValue := 10000 // After fix, this should be 10000 instead of 1

	progressValue := int(accumulatedVolume)
	if accumulatedVolume >= float64(targetValue) {
		progressValue = targetValue
	}

	shouldComplete := progressValue >= targetValue

	assert.Equal(t, 41, progressValue, "Progress should be 41 (int of 41.58)")
	assert.Equal(t, false, shouldComplete, "Task should NOT be completed with volume 41.58 and target 10000")

	t.Logf("✅ With the fix:")
	t.Logf("   - Accumulated volume: %.2f USD", accumulatedVolume)
	t.Logf("   - Target value: %d USD", targetValue)
	t.Logf("   - Progress value: %d", progressValue)
	t.Logf("   - Should complete: %t", shouldComplete)
}
